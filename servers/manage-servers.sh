#!/bin/bash
# Unified Server Management Script
# Manages both FHIR server and OMOP database services
#
# This script provides a unified interface for managing the complete
# FHIR-OMOP pipeline infrastructure

# Determine which Docker Compose command to use
if command -v docker-compose &> /dev/null; then
    COMPOSE_CMD="docker-compose"
else
    COMPOSE_CMD="docker compose"
fi

# Script configuration
ACTION=$1
SERVICE=$2
SUB_ACTION=$3

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to display usage information
usage() {
    echo "Usage: $0 [action] [service] [sub-action]"
    echo ""
    echo "Actions:"
    echo "  start     - Start services"
    echo "  stop      - Stop services"
    echo "  restart   - Restart services"
    echo "  status    - Check service status"
    echo "  logs      - Show service logs"
    echo "  setup     - Run service setup"
    echo "  clean     - Clean and reset services"
    echo "  shell     - Open service shell"
    echo ""
    echo "Services:"
    echo "  fhir      - FHIR server and database"
    echo "  omop      - OMOP database"
    echo "  all       - All services"
    echo ""
    echo "Sub-actions (for setup):"
    echo "  FHIR:     [none]"
    echo "  OMOP:     create-db, load-vocab, full"
    echo ""
    echo "Examples:"
    echo "  $0 start all              - Start all services"
    echo "  $0 start fhir             - Start FHIR server only"
    echo "  $0 setup omop full        - Complete OMOP setup"
    echo "  $0 status all             - Check all services"
    echo "  $0 shell omop             - Open OMOP PostgreSQL shell"
    echo ""
    exit 1
}

# Function to print colored output
print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to check if services are running
check_service_status() {
    local profile=$1
    $COMPOSE_CMD -f docker-compose.full.yml --profile $profile ps --services --filter "status=running" | wc -l
}

# Function to wait for service to be ready
wait_for_service() {
    local service=$1
    local timeout=30
    
    print_status $YELLOW "⏳ Waiting for $service to be ready..."
    
    case $service in
        fhir)
            while [ $timeout -gt 0 ]; do
                if curl -s http://localhost:8080/fhir/metadata > /dev/null 2>&1; then
                    print_status $GREEN "✅ FHIR server is ready"
                    return 0
                fi
                sleep 2
                timeout=$((timeout - 2))
            done
            ;;
        omop)
            while [ $timeout -gt 0 ]; do
                if $COMPOSE_CMD -f docker-compose.full.yml --profile omop exec omop-postgres pg_isready -U omop -d omop_cdm > /dev/null 2>&1; then
                    print_status $GREEN "✅ OMOP database is ready"
                    return 0
                fi
                sleep 2
                timeout=$((timeout - 2))
            done
            ;;
    esac
    
    print_status $RED "❌ $service failed to start within timeout"
    return 1
}

# Check if action is provided
if [ -z "$ACTION" ]; then
    usage
fi

# Check if service is provided
if [ -z "$SERVICE" ]; then
    usage
fi

# Execute the requested action
case $ACTION in
    start)
        case $SERVICE in
            fhir)
                print_status $BLUE "🚀 Starting FHIR server..."
                $COMPOSE_CMD -f docker-compose.full.yml --profile fhir up -d
                wait_for_service fhir
                print_status $GREEN "✅ FHIR server started: http://localhost:8080/fhir/metadata"
                ;;
            omop)
                print_status $BLUE "🚀 Starting OMOP database..."
                $COMPOSE_CMD -f docker-compose.full.yml --profile omop up -d
                wait_for_service omop
                print_status $GREEN "✅ OMOP database started"
                print_status $YELLOW "💡 Use '$0 setup omop full' to create tables and load vocabularies"
                ;;
            all)
                print_status $BLUE "🚀 Starting all services..."
                $COMPOSE_CMD -f docker-compose.full.yml --profile full up -d
                wait_for_service fhir &
                wait_for_service omop &
                wait
                print_status $GREEN "✅ All services started"
                ;;
            *)
                print_status $RED "❌ Invalid service: $SERVICE"
                usage
                ;;
        esac
        ;;
    stop)
        case $SERVICE in
            fhir)
                print_status $BLUE "🛑 Stopping FHIR server..."
                $COMPOSE_CMD -f docker-compose.full.yml --profile fhir down
                ;;
            omop)
                print_status $BLUE "🛑 Stopping OMOP database..."
                $COMPOSE_CMD -f docker-compose.full.yml --profile omop down
                ;;
            all)
                print_status $BLUE "🛑 Stopping all services..."
                $COMPOSE_CMD -f docker-compose.full.yml --profile full down
                ;;
            *)
                print_status $RED "❌ Invalid service: $SERVICE"
                usage
                ;;
        esac
        print_status $GREEN "✅ Services stopped"
        ;;
    restart)
        print_status $BLUE "🔄 Restarting $SERVICE..."
        $0 stop $SERVICE
        $0 start $SERVICE
        ;;
    status)
        print_status $BLUE "📊 Service status:"
        case $SERVICE in
            fhir)
                $COMPOSE_CMD -f docker-compose.full.yml --profile fhir ps
                ;;
            omop)
                $COMPOSE_CMD -f docker-compose.full.yml --profile omop ps
                ;;
            all)
                $COMPOSE_CMD -f docker-compose.full.yml --profile full ps
                ;;
            *)
                print_status $RED "❌ Invalid service: $SERVICE"
                usage
                ;;
        esac
        ;;
    logs)
        print_status $BLUE "📋 Showing $SERVICE logs:"
        case $SERVICE in
            fhir)
                $COMPOSE_CMD -f docker-compose.full.yml --profile fhir logs -f
                ;;
            omop)
                $COMPOSE_CMD -f docker-compose.full.yml --profile omop logs -f
                ;;
            all)
                $COMPOSE_CMD -f docker-compose.full.yml --profile full logs -f
                ;;
            *)
                print_status $RED "❌ Invalid service: $SERVICE"
                usage
                ;;
        esac
        ;;
    setup)
        case $SERVICE in
            fhir)
                print_status $YELLOW "💡 FHIR server setup is automatic on startup"
                print_status $YELLOW "💡 Access: http://localhost:8080/fhir/metadata"
                ;;
            omop)
                # Ensure OMOP database is running
                if [ $(check_service_status omop) -eq 0 ]; then
                    print_status $YELLOW "📋 OMOP database not running. Starting..."
                    $0 start omop
                fi
                
                setup_action=${SUB_ACTION:-full}
                print_status $BLUE "🔧 Running OMOP setup: $setup_action"
                
                case $setup_action in
                    create-db)
                        $COMPOSE_CMD -f docker-compose.full.yml run --rm omop-setup create-database
                        ;;
                    load-vocab)
                        $COMPOSE_CMD -f docker-compose.full.yml run --rm omop-setup load-vocabularies
                        ;;
                    full)
                        $COMPOSE_CMD -f docker-compose.full.yml run --rm omop-setup setup
                        ;;
                    *)
                        print_status $RED "❌ Invalid OMOP setup action: $setup_action"
                        usage
                        ;;
                esac
                ;;
            all)
                print_status $BLUE "🔧 Setting up all services..."
                $0 setup fhir
                $0 setup omop full
                ;;
            *)
                print_status $RED "❌ Invalid service: $SERVICE"
                usage
                ;;
        esac
        ;;
    clean)
        print_status $YELLOW "🧹 Cleaning $SERVICE..."
        print_status $RED "⚠️  This will remove all data and containers. Are you sure? (y/N)"
        read -r response
        if [[ "$response" =~ ^[Yy]$ ]]; then
            case $SERVICE in
                fhir)
                    $COMPOSE_CMD -f docker-compose.full.yml --profile fhir down -v
                    ;;
                omop)
                    $COMPOSE_CMD -f docker-compose.full.yml --profile omop down -v
                    ;;
                all)
                    $COMPOSE_CMD -f docker-compose.full.yml --profile full down -v
                    ;;
                *)
                    print_status $RED "❌ Invalid service: $SERVICE"
                    usage
                    ;;
            esac
            print_status $GREEN "✅ $SERVICE cleaned"
        else
            print_status $YELLOW "❌ Operation cancelled"
        fi
        ;;
    shell)
        case $SERVICE in
            fhir)
                if [ $(check_service_status fhir) -eq 0 ]; then
                    print_status $RED "❌ FHIR server is not running. Start it first with '$0 start fhir'"
                    exit 1
                fi
                print_status $BLUE "🔗 Opening FHIR PostgreSQL shell..."
                $COMPOSE_CMD -f docker-compose.full.yml --profile fhir exec fhir-postgres psql -U admin -d hapi
                ;;
            omop)
                if [ $(check_service_status omop) -eq 0 ]; then
                    print_status $RED "❌ OMOP database is not running. Start it first with '$0 start omop'"
                    exit 1
                fi
                print_status $BLUE "🔗 Opening OMOP PostgreSQL shell..."
                $COMPOSE_CMD -f docker-compose.full.yml --profile omop exec omop-postgres psql -U omop -d omop_cdm
                ;;
            *)
                print_status $RED "❌ Invalid service for shell: $SERVICE"
                print_status $YELLOW "💡 Available shells: fhir, omop"
                ;;
        esac
        ;;
    *)
        print_status $RED "❌ Invalid action: $ACTION"
        usage
        ;;
esac

exit 0
