# Multi-Service Docker Compose for FHIR-OMOP Pipeline
# This file orchestrates both FHIR server and OMOP database services
# Usage: docker-compose -f docker-compose.full.yml up -d

version: '3.8'

services:
  # FHIR Server from servers/fhir-server
  fhir-server:
    extends:
      file: ./fhir-server/docker-compose-postgres.yml
      service: hapi-fhir-server
    depends_on:
      - fhir-postgres
    networks:
      - fhir-network
      - integration-network
    profiles:
      - fhir
      - full

  # FHIR PostgreSQL Database
  fhir-postgres:
    extends:
      file: ./fhir-server/docker-compose-postgres.yml
      service: fhir-postgres
    networks:
      - fhir-network
    profiles:
      - fhir
      - full

  # OMOP Database from servers/omop-database
  omop-postgres:
    extends:
      file: ./omop-database/docker-compose.yml
      service: omop-postgres
    ports:
      - "5434:5432"  # Different port to avoid conflict with FHIR PostgreSQL
    networks:
      - omop-network
      - integration-network
    profiles:
      - omop
      - full

  # OMOP Setup Service
  omop-setup:
    extends:
      file: ./omop-database/docker-compose.yml
      service: omop-setup
    depends_on:
      - omop-postgres
    networks:
      - omop-network
    profiles:
      - omop-setup

  # Integration Service (Future)
  # This service will handle FHIR to OMOP transformations
  fhir-omop-transformer:
    image: fhir-omop-transformer:latest
    depends_on:
      - fhir-server
      - omop-postgres
    environment:
      - FHIR_SERVER_URL=http://fhir-server:8080/fhir
      - OMOP_DB_HOST=omop-postgres
      - OMOP_DB_PORT=5432
      - OMOP_DB_NAME=omop_cdm
    networks:
      - integration-network
    profiles:
      - transformer
      - full
    # This service is commented out until the transformer is implemented
    # build:
    #   context: ./
    #   dockerfile: Dockerfile.transformer

volumes:
  # FHIR volumes
  hapi-data:
  fhir-postgres-data:
  
  # OMOP volumes
  omop-postgres-data:

networks:
  fhir-network:
    driver: bridge
  omop-network:
    driver: bridge
  integration-network:
    driver: bridge

# Usage examples:
# docker-compose -f docker-compose.full.yml --profile fhir up -d     # Start only FHIR services
# docker-compose -f docker-compose.full.yml --profile omop up -d     # Start only OMOP services
# docker-compose -f docker-compose.full.yml --profile full up -d     # Start all services
# docker-compose -f docker-compose.full.yml run omop-setup setup     # Run OMOP setup
