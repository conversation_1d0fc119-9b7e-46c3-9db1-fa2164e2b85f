#!/bin/bash
# Docker entrypoint script for OMOP database setup

set -e

# Wait for PostgreSQL to be ready
wait_for_postgres() {
    echo "🔄 Waiting for PostgreSQL to be ready..."
    while ! pg_isready -h "$OMOP_DB_HOST" -p "$OMOP_DB_PORT" -U "$OMOP_DB_USERNAME" -d "$OMOP_DB_NAME"; do
        echo "⏳ PostgreSQL is not ready yet. Waiting..."
        sleep 2
    done
    echo "✅ PostgreSQL is ready"
}

# Function to create database and tables
create_database() {
    echo "🔧 Creating OMOP database and tables..."
    wait_for_postgres
    python -m scripts.setup.create_database
}

# Function to load vocabularies
load_vocabularies() {
    echo "📚 Loading OMOP vocabularies..."
    wait_for_postgres
    python -m scripts.setup.load_vocabularies
}

# Function to run complete setup
full_setup() {
    echo "🚀 Running complete OMOP setup..."
    create_database
    load_vocabularies
    echo "✅ OMOP setup completed successfully"
}

# Function to check database status
check_status() {
    echo "📊 Checking OMOP database status..."
    wait_for_postgres
    python -m scripts.setup.database_checker
}

# Main execution
case "$1" in
    "create-database")
        create_database
        ;;
    "load-vocabularies")
        load_vocabularies
        ;;
    "setup")
        full_setup
        ;;
    "status")
        check_status
        ;;
    *)
        echo "Usage: $0 {create-database|load-vocabularies|setup|status}"
        echo ""
        echo "Commands:"
        echo "  create-database   - Create OMOP database and tables"
        echo "  load-vocabularies - Load OMOP vocabularies"
        echo "  setup            - Run complete setup (default)"
        echo "  status           - Check database status"
        exit 1
        ;;
esac
