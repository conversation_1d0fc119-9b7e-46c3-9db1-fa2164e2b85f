#!/bin/bash
# OMOP Database Management Script
#
# This script manages the OMOP CDM database with PostgreSQL in Docker.
# Simplified implementation focused on essential operations.
#
# References:
# - OMOP CDM v5.4.2: https://ohdsi.github.io/CommonDataModel/
# - OHDSI DDL Scripts: https://github.com/OHDSI/CommonDataModel/tree/v5.4.2/PostgreSQL

# Determine which Docker Compose command to use
if command -v docker-compose &> /dev/null; then
    COMPOSE_CMD="docker-compose"
else
    COMPOSE_CMD="docker compose"
fi

# Script configuration
ACTION=$1
SUB_ACTION=$2

# Function to display usage information
usage() {
    echo "Usage: $0 [action] [sub-action]"
    echo ""
    echo "Actions:"
    echo "  start     - Start the OMOP database"
    echo "  stop      - Stop the OMOP database"
    echo "  restart   - Restart the OMOP database"
    echo "  status    - Check the status of the OMOP database"
    echo "  logs      - Show logs from the OMOP database"
    echo "  shell     - Open PostgreSQL shell"
    echo "  setup     - Run database setup (create tables, load vocabularies)"
    echo "  clean     - Clean and reset the database"
    echo ""
    echo "Sub-actions for 'setup':"
    echo "  create-db       - Create database and tables only"
    echo "  load-vocab      - Load vocabularies only"
    echo "  full           - Complete setup (default)"
    echo ""
    echo "Examples:"
    echo "  $0 start              - Start OMOP database"
    echo "  $0 status             - Check database status"
    echo "  $0 setup create-db    - Create database and tables"
    echo "  $0 shell              - Open PostgreSQL shell"
    echo ""
    exit 1
}

# Function to wait for database to be ready
wait_for_db() {
    echo "⏳ Waiting for database to be ready..."
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if $COMPOSE_CMD exec -T omop-postgres pg_isready -U omop -d omop_cdm >/dev/null 2>&1; then
            echo "✅ Database is ready"
            return 0
        fi
        echo "   Attempt $attempt/$max_attempts..."
        sleep 2
        ((attempt++))
    done
    
    echo "❌ Database failed to become ready after $max_attempts attempts"
    return 1
}

# Function to check if services are running
check_services() {
    $COMPOSE_CMD ps omop-postgres | grep -q "Up"
}

# Check if action is provided
if [ -z "$ACTION" ]; then
    usage
fi

# Execute the requested action
case $ACTION in
    start)
        echo "🚀 Starting OMOP database..."
        $COMPOSE_CMD up -d omop-postgres
        if wait_for_db; then
            echo "✅ OMOP database started successfully"
            echo "💡 Use '$0 status' to check database status"
        else
            echo "❌ Failed to start database"
            exit 1
        fi
        ;;
    stop)
        echo "🛑 Stopping OMOP database..."
        $COMPOSE_CMD down
        echo "✅ OMOP database stopped"
        ;;
    restart)
        echo "🔄 Restarting OMOP database..."
        $COMPOSE_CMD down
        $COMPOSE_CMD up -d omop-postgres
        if wait_for_db; then
            echo "✅ OMOP database restarted successfully"
        else
            echo "❌ Failed to restart database"
            exit 1
        fi
        ;;
    status)
        echo "📊 Checking OMOP database status..."
        if check_services; then
            echo "🔗 Container status:"
            $COMPOSE_CMD ps omop-postgres
            echo ""
            echo "📋 Database status:"
            # Run the Python status checker
            cd "$(dirname "$0")"
            python3 scripts/setup/database_checker.py
        else
            echo "❌ OMOP database is not running"
            echo "💡 Use '$0 start' to start the database"
        fi
        ;;
    logs)
        echo "📜 Showing OMOP database logs..."
        $COMPOSE_CMD logs -f omop-postgres
        ;;
    setup)
        if ! check_services; then
            echo "❌ Database is not running. Start it first with '$0 start'"
            exit 1
        fi
        
        # Determine setup action
        setup_action=${SUB_ACTION:-full}
        
        echo "🔧 Running OMOP database setup..."
        cd "$(dirname "$0")"
        
        case $setup_action in
            create-db)
                echo "🏗️  Creating OMOP database and tables..."
                python3 scripts/setup/create_database.py
                ;;
            load-vocab)
                echo "📚 Loading OMOP vocabularies..."
                python3 scripts/setup/load_vocabularies.py
                ;;
            full)
                echo "🚀 Running complete OMOP setup..."
                echo "⚠️  This process may take 20-60 minutes depending on vocabulary size"
                python3 scripts/setup/create_database.py
                if [ $? -eq 0 ]; then
                    python3 scripts/setup/load_vocabularies.py
                else
                    echo "❌ Database creation failed, skipping vocabulary loading"
                    exit 1
                fi
                ;;
            *)
                echo "❌ Invalid setup action: $setup_action"
                usage
                ;;
        esac
        ;;
    clean)
        echo "🧹 Cleaning OMOP database..."
        echo "⚠️  This will remove all data and containers. Are you sure? (y/N)"
        read -r response
        if [[ "$response" =~ ^[Yy]$ ]]; then
            $COMPOSE_CMD down -v
            docker volume rm omop-database_omop-postgres-data 2>/dev/null || true
            echo "✅ OMOP database cleaned"
        else
            echo "❌ Operation cancelled"
        fi
        ;;
    shell)
        if ! check_services; then
            echo "❌ Database is not running. Start it first with '$0 start'"
            exit 1
        fi
        echo "🔗 Opening PostgreSQL shell..."
        $COMPOSE_CMD exec omop-postgres psql -U omop -d omop_cdm
        ;;
    *)
        echo "❌ Invalid action: $ACTION"
        usage
        ;;
esac

exit 0
