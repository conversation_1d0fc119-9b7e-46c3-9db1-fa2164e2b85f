services:
  # OMOP CDM Database with PostgreSQL
  # Based on OHDSI official specifications for OMOP CDM v5.4.2
  # https://ohdsi.github.io/CommonDataModel/
  omop-postgres:
    image: postgres:14                              # PostgreSQL 14 for compatibility
    container_name: omop-postgres
    ports:
      - "${OMOP_DB_PORT:-5432}:5432"               # Expose PostgreSQL port
    environment:
      # Database configuration
      POSTGRES_DB: ${OMOP_DB_NAME:-omop_cdm}
      POSTGRES_USER: ${OMOP_DB_USERNAME:-omop}
      POSTGRES_PASSWORD: ${OMOP_DB_PASSWORD:-omop_secure_2024}
      
      # Additional PostgreSQL configuration for OMOP
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --lc-collate=C --lc-ctype=C"
    volumes:
      # Persistent volume for OMOP data
      - omop-postgres-data:/var/lib/postgresql/data
      
      # Mount DDL scripts directory
      - ./scripts/ddl:/docker-entrypoint-initdb.d/ddl:ro
      
      # Mount vocabulary data directory
      - ${VOCABULARY_PATH:-./data/vocabulary}:/data/vocabulary:ro
      
      # Mount initialization scripts
      - ./scripts/init:/docker-entrypoint-initdb.d:ro
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${OMOP_DB_USERNAME:-omop} -d ${OMOP_DB_NAME:-omop_cdm}"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - omop-network

  # OMOP Database Setup Service
  # This service runs the database creation and vocabulary loading
  omop-setup:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: omop-setup
    depends_on:
      omop-postgres:
        condition: service_healthy
    environment:
      # Database connection
      OMOP_DB_HOST: omop-postgres
      OMOP_DB_PORT: 5432
      OMOP_DB_NAME: ${OMOP_DB_NAME:-omop_cdm}
      OMOP_DB_USERNAME: ${OMOP_DB_USERNAME:-omop}
      OMOP_DB_PASSWORD: ${OMOP_DB_PASSWORD:-omop_secure_2024}
      
      # Admin configuration
      POSTGRES_ADMIN_USER: ${OMOP_DB_USERNAME:-omop}
      POSTGRES_ADMIN_DB: ${OMOP_DB_NAME:-omop_cdm}
      
      # Vocabulary configuration
      VOCABULARY_PATH: /data/vocabulary
      
      # Processing configuration
      BATCH_SIZE: ${BATCH_SIZE:-100}
      MAX_WORKERS: ${MAX_WORKERS:-4}
    volumes:
      # Mount vocabulary data
      - ${VOCABULARY_PATH:-./data/vocabulary}:/data/vocabulary:ro
      
      # Mount logs directory
      - ./logs:/app/logs
    networks:
      - omop-network
    profiles:
      - setup  # Only run when explicitly requested

volumes:
  omop-postgres-data:
    driver: local

networks:
  omop-network:
    driver: bridge
