services:
  # OMOP CDM Database with PostgreSQL
  # Based on OHDSI official specifications for OMOP CDM v5.4.2
  # https://ohdsi.github.io/CommonDataModel/
  omop-postgres:
    image: postgres:14                              # PostgreSQL 14 for compatibility
    container_name: omop-postgres
    ports:
      - "${OMOP_EXTERNAL_PORT:-5434}:5432"         # Expose PostgreSQL port (avoid conflict with FHIR)
    environment:
      # Database configuration
      POSTGRES_DB: ${OMOP_DB_NAME:-omop_cdm}
      POSTGRES_USER: ${OMOP_DB_USERNAME:-omop}
      POSTGRES_PASSWORD: ${OMOP_DB_PASSWORD:-omop_secure_2024}
      
      # Additional PostgreSQL configuration for OMOP
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --lc-collate=C --lc-ctype=C"
    volumes:
      # Persistent volume for OMOP data
      - omop-postgres-data:/var/lib/postgresql/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${OMOP_DB_USERNAME:-omop} -d ${OMOP_DB_NAME:-omop_cdm}"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - omop-network

volumes:
  omop-postgres-data:
    driver: local

networks:
  omop-network:
    driver: bridge
