# OMOP Database Setup Requirements
# Core dependencies for OMOP database setup and management

# Database connectivity
psycopg2-binary>=2.9.0

# Environment and configuration
python-dotenv>=1.0.0

# HTTP requests (for DDL script downloads)
requests>=2.31.0

# CLI and utilities
click>=8.0.0
pydantic>=2.0.0

# Logging and progress
rich>=13.0.0
tqdm>=4.64.0

# Data processing
pandas>=2.0.0
numpy>=1.24.0

# Path handling
pathlib2>=2.3.0

# Testing (for validation)
pytest>=7.0.0
pytest-cov>=4.0.0
