# Contributing to FHIR to OMOP CDM Transformation Pipeline

This document provides guidelines and instructions for contributing to the FHIR to OMOP CDM Transformation Pipeline, an internal project of AIO (Artificial Intelligence Orchestrator).

## Technology Stack

This project uses specific versions for compatibility and stability:

- **OMOP CDM**: Version 5.4.2 for all database schemas and references
- **HAPI FHIR**: R4 with `hapiproject/hapi:latest` Docker image
- **PostgreSQL**: Version 14 (avoid v15+ due to HAPI FHIR compatibility issues)
- **Python Environment**: Conda environment 'fhir-omop'

## Development Setup

1. Clone the repository
2. Create and activate the Conda environment:
   ```bash
   # Create and activate the Conda environment
   conda env create -f environment.yml
   conda activate fhir-omop
   ```
3. Copy `.env.example` to `.env` and configure as needed
4. Ensure you have Docker Desktop installed for FHIR server deployment

## Coding Standards

For detailed coding and documentation standards, please refer to our comprehensive [standards guide](docs/guides/development/standards.md). This document includes detailed guidelines for:

- Documentation format and structure
- Python, SQL, and YAML coding styles
- File organization and naming conventions
- References and citations format
- Version control practices

### Development Methodology

All development should follow our pedagogical, academic methodology:

- Apply detailed step-by-step explanations for technical implementations
- Include "why/what/how" breakdowns for complex processes
- Reference official sources with direct links and version information
- Validate against official OMOP CDM documentation before implementation
- Use Context7 MCP for accessing official documentation when available

### Python Coding Standards Summary

All Python code must adhere to the following standards:

1. **Style Guide**: Follow [PEP 8](https://www.python.org/dev/peps/pep-0008/) conventions.

2. **Type Hints**: Use type hints for all function parameters and return values.
   ```python
   def process_patient(patient_id: str, include_observations: bool = False) -> Dict[str, Any]:
       """Process a patient and return their data."""
       # Implementation
   ```

3. **Docstrings**: Use NumPy-style docstrings for all functions, classes, and modules.
   ```python
   def function_with_types(param1: int, param2: str) -> bool:
       """Example function with NumPy style docstrings.

       Parameters
       ----------
       param1 : int
           The first parameter.
       param2 : str
           The second parameter.

       Returns
       -------
       bool
           True if successful, False otherwise.
       """
       return True
   ```

4. **Imports**: Organize imports in the following order with blank lines between groups:
   - Standard library imports
   - Related third-party imports
   - Local application/library specific imports

5. **Environment Variables**: All scripts must support environment variables with dotenv:
   ```python
   import os
   from dotenv import load_dotenv

   # Load environment variables from root .env file
   load_dotenv()

   # Use environment variables with defaults
   SERVER_URL = os.getenv("FHIR_SERVER_URL", "http://localhost:8080/fhir")
   ```

6. **Script Structure**: Follow the consistent structure pattern defined in standards.md:
   - Command-line argument parsing with argparse
   - Environment variable support with dotenv
   - Configuration precedence: command-line args > environment variables > defaults

## Commit Messages

Please use clear and descriptive commit messages. Follow these guidelines:

- Use the present tense ("Add feature" not "Added feature")
- Use the imperative mood ("Move cursor to..." not "Moves cursor to...")
- Limit the first line to 72 characters or less
- Reference issues and pull requests after the first line

## Testing and Quality Assurance

Run tests using pytest:

```bash
# Activate the conda environment
conda activate fhir-omop

# Run all tests
pytest

# Run tests with coverage
pytest --cov=src/fhir_omop
```

### Testing Requirements

- Write unit tests for all new functionality
- Include data validation tolerances (5% variance) for ETL processes
- Implement comprehensive error handling with academic explanations
- Validate outputs at important steps with interpretation guidance
- Ensure all tests pass before submitting a pull request

## Documentation Standards

Update documentation when making changes to the code. All documentation must follow our established standards:

### Documentation Requirements

- **Language**: Write all documentation in English using Markdown format
- **Structure**: Title → Brief Description → TOC → Main Content → References
- **Headers**: Use ATX-style headers with one space after # character
- **Citations**: Use inline hyperlinked citations rather than numbered references
- **Version Information**: Include version information when referencing software or standards
- **Diagrams**: Include Mermaid diagrams for complex processes and architecture

### Files to Update

- README.md files
- NumPy-style docstrings in code
- Inline comments for complex logic
- Relevant documentation files in docs/ directory
- Update existing documentation rather than creating redundant files

## Project-Specific Guidelines

### Database Configuration

- Use localhost:5432 for local PostgreSQL connections
- Database naming: 'omop_abu_dhabi' for main, 'omop_abu_dhabi_test' for testing
- Use 'public' schema for OMOP CDM tables
- Follow official OHDSI loading order for vocabulary implementation

### Architecture Patterns

- Base OMOP module implementation on HL7 Vulcan FHIR-to-OMOP Implementation Guide
- Follow Docker architecture patterns from servers/fhir-server implementation
- Use transaction bundle approach with permissive mode for large FHIR datasets
- Maintain modular, reusable components in shared modules

### Package Management

- Always use package managers (conda, pip) instead of manually editing package files
- Use conda for environment management, pip for package installation within conda env
- Remove unnecessary dependencies when implementation decisions change
- Maintain clean environment.yml file aligned with actual project needs

## Proprietary Notice

By contributing to this project, you acknowledge that this is a proprietary project of AIO (Artificial Intelligence Orchestrator). All contributions become the property of AIO and are subject to internal confidentiality policies.
