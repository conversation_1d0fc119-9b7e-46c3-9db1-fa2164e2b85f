# GitHub Copilot Instructions for FHIR-OMOP Project

## Technology Stack Specifications

We use specific versions for compatibility and stability:

- **OMOP CDM**: Version 5.4.2 for all database schemas and references
- **HAPI FHIR**: R4 with `hapiproject/hapi:latest` Docker image  
- **PostgreSQL**: Version 14 (avoid v15+ due to HAPI FHIR compatibility issues)
- **Python Environment**: Conda environment 'fhir-omop'
- **Docker**: Version 20.10.x+ with Compose v2.x

## Code Standards and Patterns

### Python Development
- Follow NumPy-style docstrings for all functions with type hints
- Use environment variable support with dotenv for all scripts
- Use relative paths to reference the root .env file instead of creating separate ones
- Maximum line length of 88 characters (compatible with Black formatter)
- Organize imports: standard library → third-party → local application imports

### Script Structure Pattern
All scripts should follow this consistent structure:
- Command-line argument parsing with argparse
- Environment variable support with dotenv  
- Configuration precedence: command-line args > environment variables > defaults

### Database Patterns
- Use PostgreSQL COPY method for vocabulary loading following OHDSI standards
- Implement existence detection (check if database exists before creating)
- Use localhost:5432 for local PostgreSQL connections
- Database naming: 'omop_abu_dhabi' for main, 'omop_abu_dhabi_test' for testing
- Use 'public' schema for OMOP CDM tables

### Docker Architecture
- Follow Docker architecture patterns from servers/fhir-server implementation
- Use transaction bundle approach with permissive mode for large FHIR datasets
- Maintain modular, reusable components in shared modules

## Development Methodology

Apply pedagogical, academic methodology to ALL processes:
- Include detailed step-by-step explanations for technical implementations
- Provide "why/what/how" breakdowns for complex processes
- Reference official sources with direct links and version information
- Validate against official OMOP CDM v5.4.2 documentation before implementation
- Use Context7 MCP for accessing official documentation when available

## Project-Specific Architecture

- Base OMOP module implementation on HL7 Vulcan FHIR-to-OMOP Implementation Guide
- Follow reference_architecture.md and development standards.md for automation planning
- Prioritize fresh OMOP database deployments following official documentation
- Use dynamic folder detection with prefixes for vocabulary paths (avoid hardcoded dates)
- Follow phase-based development: Phase 1 (HAPI FHIR) → Phase 2 (OMOP database)

## Documentation Requirements

- Write all documentation in English using Markdown format
- Structure as: Title → Brief Description → TOC → Main Content → References
- Use ATX-style headers with one space after # character
- Include Mermaid diagrams for complex processes and architecture
- Use inline hyperlinked citations rather than numbered references
- Include version information when referencing software or standards

## Testing and Quality Assurance

- Write unit tests for all new functionality using pytest
- Include data validation tolerances (5% variance) for ETL processes
- Implement comprehensive error handling with academic explanations
- Validate outputs at important steps with interpretation guidance

## Version Control Practices

- Use branch format: type/description (e.g., feature/add-patient-mapper)
- Write commit messages in imperative mood with capital first letter
- Include descriptive PR descriptions with testing information
- Reference issues and related work in commit messages

## Abu Dhabi Claims Specifics

- Integrate Shafafiya Dictionary analysis for UAE healthcare vocabulary mapping
- Use technical, logical, incremental approach for CSV-to-OMOP mapping analysis
- Include expected metrics validation (3185 records, 447 patients, 1201 visits, 171 providers)
- Plan for client dataset limitations and vocabulary format verification

## Package Management

- Always use package managers (conda, pip) instead of manually editing package files
- Use conda for environment management, pip for package installation within conda env
- Remove unnecessary dependencies when implementation decisions change
- Maintain clean environment.yml file aligned with actual project needs
