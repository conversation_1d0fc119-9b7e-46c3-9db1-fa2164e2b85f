# Coding and Documentation Standards

This document defines the standards, formats, and styles to be used throughout the FHIR to OMOP CDM Transformation Pipeline project. Following these guidelines ensures consistency across all project components.

## Technology Stack Specifications

This project uses specific versions for compatibility and stability:

- **OMOP CDM**: Version 5.4.2 for all database schemas and references
- **HAPI FHIR**: R4 with `hapiproject/hapi:latest` Docker image
- **PostgreSQL**: Version 14 (avoid v15+ due to HAPI FHIR compatibility issues)
- **Python Environment**: Conda environment 'fhir-omop'
- **Development Tools**: Context7 MCP for accessing official documentation

## Table of Contents

- [Technology Stack Specifications](#technology-stack-specifications)
- [Documentation Standards](#documentation-standards)
  - [Markdown Style](#markdown-style)
  - [Documentation Structure](#documentation-structure)
  - [References and Citations](#references-and-citations)
- [Code Standards](#code-standards)
  - [Python Style](#python-style)
  - [SQL Style](#sql-style)
  - [YAML Style](#yaml-style)
- [File Organization](#file-organization)
- [Naming Conventions](#naming-conventions)
- [Version Control Practices](#version-control-practices)

## Documentation Standards

### Markdown Style

All documentation should be written in Markdown format with the following guidelines:

1. **Language**: All documentation should be written in English.
2. **Headers**: Use ATX-style headers (with `#` symbols). Add one space after the `#` character.
   ```markdown
   # Level 1 Header
   ## Level 2 Header
   ### Level 3 Header
   ```

3. **Lists**: Use `-` for unordered lists and numbers for ordered lists. Indent nested lists with 2 spaces.
   ```markdown
   - Item 1
     - Nested item
   - Item 2

   1. First step
   2. Second step
   ```

4. **Code Blocks**: Use triple backticks with language specification for code blocks.
   ```markdown
   ```python
   def example_function():
       return "This is an example"
   ```
   ```

5. **Tables**: Use standard Markdown tables with header row and alignment indicators.
   ```markdown
   | Column 1 | Column 2 | Column 3 |
   |----------|:--------:|---------:|
   | Left     | Center   | Right    |
   ```

6. **Emphasis**: Use single asterisks for *italic* and double asterisks for **bold**.

7. **Links**: Use inline links with descriptive text.
   ```markdown
   [Link text](https://example.com "Optional title")
   ```

### Documentation Structure

Each documentation file should follow this structure:

1. **Title**: Start with a level 1 header (`#`) containing the document title.
2. **Brief Description**: A short paragraph explaining the purpose of the document.
3. **Table of Contents**: For documents longer than 3 sections.
4. **Main Content**: Organized in logical sections with appropriate headers.
5. **References**: If applicable, include references at the end of the document.

### References and Citations

For references and citations:

1. **Inline Citations**: Use hyperlinked keywords directly in the text rather than numbered references.
   ```markdown
   The [OHDSI documentation](https://ohdsi.github.io/TheBookOfOhdsi/) provides guidance on...
   ```

2. **Direct Links**: Always use direct links to specific resources rather than general website links.

3. **Version Information**: When referencing software or standards, include version information.
   ```markdown
   FHIR R4 (v4.0.1) defines...
   ```

## Code Standards

### Python Style

1. **Style Guide**: Follow [PEP 8](https://www.python.org/dev/peps/pep-0008/) conventions.
2. **Docstrings**: Use [NumPy-style docstrings](https://numpydoc.readthedocs.io/en/latest/format.html).
   ```python
   def function_with_types_in_docstring(param1, param2):
       """Example function with types documented in the docstring.

       Parameters
       ----------
       param1 : int
           The first parameter.
       param2 : str
           The second parameter.

       Returns
       -------
       bool
           The return value. True for success, False otherwise.
       """
       return True
   ```

3. **Type Hints**: Use type hints for function parameters and return values.
   ```python
   def greeting(name: str) -> str:
       return f"Hello {name}"
   ```

4. **Imports**: Organize imports in the following order, with a blank line between each group:
   - Standard library imports
   - Related third-party imports
   - Local application/library specific imports

5. **Line Length**: Maximum line length of 88 characters (compatible with Black formatter).

6. **Naming Conventions**:
   - Classes: `CamelCase`
   - Functions and variables: `snake_case`
   - Constants: `UPPER_CASE_WITH_UNDERSCORES`
   - Private methods/variables: Prefix with underscore (`_private_method`)

7. **Script Structure**: All scripts should follow a consistent structure:
   - Command-line argument parsing with `argparse`
   - Environment variable support with `dotenv`
   - Configuration precedence: command-line args > environment variables > defaults

   ```python
   import os
   import argparse
   from dotenv import load_dotenv

   # Load environment variables
   load_dotenv()

   def parse_args():
       """Parse command line arguments."""
       parser = argparse.ArgumentParser(description="Script description")
       parser.add_argument(
           "--server-url",
           default=os.getenv("FHIR_SERVER_URL", "http://localhost:8080/fhir"),
           help="URL of the FHIR server"
       )
       # Add other arguments as needed
       return parser.parse_args()

   # Parse command line arguments
   args = parse_args()

   # Use arguments in the script
   SERVER_URL = args.server_url
   ```

8. **Standard Arguments**: Use consistent argument names across scripts:

   | Argument | Environment Variable | Default | Description |
   |----------|---------------------|---------|-------------|
   | `--server-url` | `FHIR_SERVER_URL` | `http://localhost:8080/fhir` | FHIR server URL |
   | `--input-file` | `INPUT_FILE` | - | Input file path |
   | `--output-file` | `OUTPUT_FILE` | - | Output file path |
   | `--data-dir` | `DATA_DIR` | - | Data directory path |

### SQL Style

1. **Keywords**: Use UPPERCASE for SQL keywords.
   ```sql
   SELECT column_name FROM table_name WHERE condition;
   ```

2. **Identifiers**: Use snake_case for table and column names.

3. **Indentation**: Indent subqueries and conditions with 4 spaces.
   ```sql
   SELECT
       t.column1,
       t.column2
   FROM table_name t
   WHERE
       t.column1 = 'value'
       AND t.column2 > 100;
   ```

4. **Aliases**: Use meaningful aliases for tables and columns.

### YAML Style

1. **Indentation**: Use 2 spaces for indentation.
   ```yaml
   parent:
     child: value
     another_child: value
   ```

2. **Lists**: Use hyphen with a space for list items.
   ```yaml
   items:
     - item1
     - item2
   ```

3. **Quotes**: Use quotes for strings with special characters or that could be interpreted as other types.
   ```yaml
   string_value: "This is a string with: special characters"
   ```

## File Organization

1. **Directory Structure**: Follow the project's established directory structure.

2. **File Naming**: Use descriptive, lowercase names with underscores for spaces.
   - Python files: `module_name.py`
   - Markdown files: `document_name.md`
   - Configuration files: `config_name.yml` or `config_name.yaml`

3. **File Headers**: Include a header comment in each file with:
   - Brief description
   - Author information
   - Creation date
   - License information

## Naming Conventions

1. **Files and Directories**: Use lowercase with underscores (`file_name.py`, `directory_name/`).

2. **Branches**: Use the format `type/description` (e.g., `feature/add-patient-mapper`, `bugfix/fix-date-conversion`).

3. **Documentation Files**: Use descriptive names that indicate the content (`patient_mapping_guide.md`).

## Version Control Practices

1. **Commit Messages**: Write clear, concise commit messages in the imperative mood.
   - Start with a capital letter
   - Use the imperative mood ("Add feature" not "Added feature")
   - Keep the first line under 72 characters
   - Provide more details in the commit body if necessary

   Example:
   ```
   Add Patient to Person mapper with demographic handling

   - Implements basic demographic mapping
   - Handles gender standardization
   - Preserves original FHIR IDs in source_value fields
   ```

2. **Branching Strategy**:
   - `main`: Production-ready code
   - `develop`: Integration branch for features
   - Feature branches: Created from `develop` for new features
   - Hotfix branches: Created from `main` for urgent fixes

3. **Pull Requests**: Include a description of changes, reference to related issues, and testing information.

---

This document will be updated as new standards are established or existing ones are refined. All team members should review this document regularly to ensure compliance with the project's standards.
